import { prisma } from "@/lib/prisma";

export default async function ReceptionDashboard() {
  const bookings = await prisma.booking.findMany({
    include: { resort: true, spaTreatment: true },
  });

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Reception Dashboard</h1>
      <ul>
        {bookings.map((b) => (
          <li key={b.id}>
            {b.userEmail} - {b.resort?.name || b.spaTreatment?.name} -{" "}
            {new Date(b.checkIn).toDateString()}
          </li>
        ))}
      </ul>
    </div>
  );
}
