const nodemailer = require("nodemailer");
require("dotenv").config();

async function testEmailSystem() {
  console.log("🧪 Testing Email Verification System...\n");

  // Test 1: Environment Variables
  console.log("1️⃣ Testing Environment Variables...");
  const requiredVars = [
    "EMAIL_HOST",
    "EMAIL_PORT",
    "EMAIL_USER",
    "EMAIL_PASS",
    "EMAIL_SECURE",
  ];
  let allVarsPresent = true;

  requiredVars.forEach((varName) => {
    if (process.env[varName]) {
      console.log(`✅ ${varName}: SET`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
      allVarsPresent = false;
    }
  });

  if (!allVarsPresent) {
    console.log("❌ Missing required environment variables");
    return false;
  }

  // Test 2: Email Configuration
  console.log("\n2️⃣ Testing Email Configuration...");
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: Number(process.env.EMAIL_PORT),
      secure: process.env.EMAIL_SECURE === "true",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
      tls: {
        rejectUnauthorized: true,
      },
    });

    await transporter.verify();
    console.log("✅ Email configuration is valid");

    // Test 3: Send Test Email
    console.log("\n3️⃣ Testing Email Sending...");
    const testEmail = {
      from: `"Kuriftu Resorts Test" <${process.env.EMAIL_USER}>`,
      to: process.env.EMAIL_USER, // Send to self for testing
      subject: "Email System Test - Kuriftu Resorts",
      html: `
        <h2>Email System Test</h2>
        <p>This is a test email to verify that the email system is working correctly.</p>
        <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
        <p>✅ Email verification system is operational!</p>
      `,
    };

    const info = await transporter.sendMail(testEmail);
    console.log("✅ Test email sent successfully!");
    console.log("📧 Message ID:", info.messageId);
  } catch (error) {
    console.log("❌ Email configuration error:", error.message);
    return false;
  }

  console.log("\n🎉 Email system testing completed successfully!");
  return true;
}

// Run tests if this script is executed directly
if (require.main === module) {
  testEmailSystem()
    .then(() => {
      console.log("✅ Email verification system is ready!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Test runner error:", error);
      process.exit(1);
    });
}

module.exports = { testEmailSystem };
