import { prisma } from "@/lib/prisma";
import Image from "next/image";
import { notFound } from "next/navigation";
import ReviewsList from "@/app/components/ReviewsList";
import ReviewForm from "@/app/components/ReviewForm";
import { Suspense } from "react";

export default async function ResortDetail({
  params,
}: {
  params: { slug: string };
}) {
  const resort = await prisma.resort.findUnique({
    where: { slug: params.slug },
  });

  if (!resort) return notFound();

  const handleReviewSubmit = async (data: any) => {
    "use server";
    // This will be handled by the client-side form
  };

  return (
    <div className="max-w-5xl mx-auto px-4 py-10">
      <Image
        src={resort.image}
        alt={resort.name}
        className="w-full h-[400px] object-cover rounded-xl mb-6"
        width={800}
        height={400}
      />
      <h1 className="text-4xl font-bold mb-4">{resort.name}</h1>
      <p className="text-lg mb-8">{resort.description}</p>

      {/* Reviews Section */}
      <div className="mt-12 space-y-8">
        <Suspense
          fallback={
            <div className="animate-pulse h-32 bg-gray-100 rounded-lg"></div>
          }
        >
          <ReviewForm
            contentType="resort"
            contentId={resort.id}
            contentName={resort.name}
            onSubmit={async (data) => {
              const response = await fetch("/api/reviews", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(data),
              });
              if (!response.ok) {
                throw new Error("Failed to submit review");
              }
            }}
          />
        </Suspense>

        <Suspense
          fallback={
            <div className="animate-pulse h-64 bg-gray-100 rounded-lg"></div>
          }
        >
          <ReviewsList
            contentType="resort"
            contentId={resort.id}
            contentName={resort.name}
            showFilters={true}
            showStats={true}
            maxItems={10}
          />
        </Suspense>
      </div>
    </div>
  );
}
