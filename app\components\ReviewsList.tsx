"use client";

import { useState, useEffect } from "react";
import { Review, ReviewFilters, ReviewListResponse } from "@/app/types/review";
import ReviewCard from "./ReviewCard";
import StarRating from "./StarRating";
import { motion } from "framer-motion";

interface ReviewsListProps {
  contentType?: 'resort' | 'room' | 'spa' | 'experience' | 'wellness' | 'event';
  contentId?: string;
  contentName?: string;
  showFilters?: boolean;
  showStats?: boolean;
  maxItems?: number;
  className?: string;
}

export default function ReviewsList({
  contentType,
  contentId,
  contentName,
  showFilters = true,
  showStats = true,
  maxItems = 10,
  className = "",
}: ReviewsListProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ReviewFilters>({
    contentType,
    contentId,
    sortBy: 'newest',
    page: 1,
    limit: maxItems,
  });
  const [stats, setStats] = useState<{
    total: number;
    averageRating?: number;
    ratingDistribution?: any;
  }>({ total: 0 });

  const fetchReviews = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (filters.contentType) params.append('contentType', filters.contentType);
      if (filters.contentId) params.append('contentId', filters.contentId);
      if (filters.rating) params.append('rating', filters.rating.toString());
      if (filters.minRating) params.append('minRating', filters.minRating.toString());
      if (filters.isVerified) params.append('isVerified', 'true');
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/reviews?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch reviews');
      }

      const data: ReviewListResponse = await response.json();
      setReviews(data.reviews);
      setStats({
        total: data.total,
        averageRating: data.averageRating,
        ratingDistribution: data.ratingDistribution,
      });
    } catch (err) {
      console.error('Error fetching reviews:', err);
      setError('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, [filters]);

  const handleFilterChange = (newFilters: Partial<ReviewFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handleHelpful = async (reviewId: string) => {
    try {
      const response = await fetch(`/api/reviews/${reviewId}/helpful`, {
        method: 'POST',
      });

      if (response.ok) {
        // Refresh reviews to get updated helpful count
        fetchReviews();
      }
    } catch (error) {
      console.error('Error marking review as helpful:', error);
    }
  };

  const handleReport = async (reviewId: string) => {
    // For now, just show an alert. In a real app, you'd implement a report system
    alert('Review reported. Thank you for helping us maintain quality.');
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-6">
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={fetchReviews}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-2xl font-bold text-gray-900">
          {contentName ? `Reviews for ${contentName}` : 'Reviews'}
        </h3>
        
        {stats.total > 0 && (
          <div className="text-sm text-gray-600">
            {stats.total} review{stats.total !== 1 ? 's' : ''}
          </div>
        )}
      </div>

      {/* Stats */}
      {showStats && stats.averageRating && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-50 rounded-lg p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <StarRating rating={stats.averageRating} size="lg" />
                <span className="text-2xl font-bold text-gray-900">
                  {stats.averageRating.toFixed(1)}
                </span>
                <span className="text-gray-600">
                  ({stats.total} review{stats.total !== 1 ? 's' : ''})
                </span>
              </div>
            </div>

            {stats.ratingDistribution && (
              <div className="space-y-1">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="flex items-center space-x-2 text-sm">
                    <span className="w-8">{rating}★</span>
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full"
                        style={{
                          width: `${stats.total > 0 ? (stats.ratingDistribution[rating] / stats.total) * 100 : 0}%`
                        }}
                      ></div>
                    </div>
                    <span className="w-8 text-gray-600">
                      {stats.ratingDistribution[rating] || 0}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="flex flex-wrap items-center gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Sort by:</label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange({ sortBy: e.target.value as any })}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="newest">Newest</option>
              <option value="oldest">Oldest</option>
              <option value="rating_high">Highest Rating</option>
              <option value="rating_low">Lowest Rating</option>
              <option value="helpful">Most Helpful</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">Rating:</label>
            <select
              value={filters.minRating || ''}
              onChange={(e) => handleFilterChange({ 
                minRating: e.target.value ? parseInt(e.target.value) : undefined 
              })}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Ratings</option>
              <option value="4">4+ Stars</option>
              <option value="3">3+ Stars</option>
              <option value="2">2+ Stars</option>
              <option value="1">1+ Stars</option>
            </select>
          </div>

          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={filters.isVerified || false}
              onChange={(e) => handleFilterChange({ isVerified: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-gray-700">Verified guests only</span>
          </label>
        </div>
      )}

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-6">
          {reviews.map((review, index) => (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <ReviewCard
                review={review}
                showActions={true}
                onHelpful={handleHelpful}
                onReport={handleReport}
              />
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-gray-900 mb-2">No reviews yet</h4>
          <p className="text-gray-600">
            {contentName ? `Be the first to review ${contentName}!` : 'Be the first to write a review!'}
          </p>
        </div>
      )}
    </div>
  );
}
