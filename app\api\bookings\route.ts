import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { sendBookingConfirmation } from "@/lib/mailer";
import { MAX_BOOKINGS_PER_DAY } from "@/lib/constants";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  CreateBookingSchema,
  validateRequestBody,
  sanitizeString,
} from "@/lib/validation";
import { logAuditEvent } from "@/lib/security";

export async function POST(req: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      await logAuditEvent({
        action: "UNAUTHORIZED_BOOKING_ATTEMPT",
        resource: "/api/bookings",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: "No authentication session",
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const rawData = await req.json();

    // Validate input data
    const validation = validateRequestBody(CreateBookingSchema, {
      ...rawData,
      userEmail: session.user.email, // Force use of authenticated user's email
    });

    if (!validation.success) {
      await logAuditEvent({
        userId: session.user.id,
        action: "INVALID_BOOKING_DATA",
        resource: "/api/bookings",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        success: false,
        error: validation.error,
      });
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const data = validation.data;

    // Verify resource exists
    if (data.resortId) {
      const resort = await prisma.resort.findUnique({
        where: { id: data.resortId },
      });
      if (!resort) {
        return NextResponse.json(
          { error: "Resort not found" },
          { status: 404 }
        );
      }
    }

    if (data.spaId) {
      const spa = await prisma.spaTreatment.findUnique({
        where: { id: data.spaId },
      });
      if (!spa) {
        return NextResponse.json(
          { error: "Spa treatment not found" },
          { status: 404 }
        );
      }
    }

    // Check availability
    const checkInDate = new Date(data.checkIn);
    const existingBookings = await prisma.booking.findMany({
      where: {
        checkIn: {
          gte: new Date(checkInDate.toISOString().split("T")[0] + "T00:00:00Z"),
          lt: new Date(checkInDate.toISOString().split("T")[0] + "T23:59:59Z"),
        },
        ...(data.resortId
          ? { resortId: data.resortId }
          : { spaId: data.spaId }),
        status: { not: "CANCELLED" }, // Don't count cancelled bookings
      },
    });

    if (existingBookings.length >= MAX_BOOKINGS_PER_DAY) {
      return NextResponse.json(
        { error: "Fully booked on this date" },
        { status: 400 }
      );
    }

    // Validate dates
    const now = new Date();
    if (checkInDate < now) {
      return NextResponse.json(
        { error: "Check-in date cannot be in the past" },
        { status: 400 }
      );
    }

    if (data.checkOut) {
      const checkOutDate = new Date(data.checkOut);
      if (checkOutDate <= checkInDate) {
        return NextResponse.json(
          { error: "Check-out date must be after check-in date" },
          { status: 400 }
        );
      }
    }

    // Create booking
    const booking = await prisma.booking.create({
      data: {
        userEmail: data.userEmail,
        resortId: data.resortId || null,
        spaId: data.spaId || null,
        checkIn: checkInDate,
        checkOut: data.checkOut ? new Date(data.checkOut) : null,
        notes: data.notes ? sanitizeString(data.notes) : null,
        status: "PENDING",
      },
      include: {
        resort: true,
        spaTreatment: true,
      },
    });

    // Send confirmation email with enhanced template
    const emailData = {
      bookingId: booking.id,
      customerName: session.user.name,
      type: data.resortId ? "Resort" : "Spa Treatment",
      name: booking.resort?.name || booking.spaTreatment?.name,
      location: booking.resort?.location,
      checkIn: checkInDate.toLocaleDateString(),
      checkOut: data.checkOut
        ? new Date(data.checkOut).toLocaleDateString()
        : null,
      status: booking.status,
      notes: booking.notes,
    };

    try {
      await sendBookingConfirmation(data.userEmail, emailData);
    } catch (emailError) {
      console.error("Failed to send booking confirmation:", emailError);
      // Don't fail the booking if email fails
    }

    await logAuditEvent({
      userId: session.user.id,
      action: "BOOKING_CREATED",
      resource: "/api/bookings",
      resourceId: booking.id,
      ip: req.headers.get("x-forwarded-for") || "unknown",
      userAgent: req.headers.get("user-agent") || "unknown",
      success: true,
    });

    return NextResponse.json(booking);
  } catch (error) {
    console.error("Error creating booking:", error);
    return NextResponse.json(
      { error: "Failed to create booking" },
      { status: 500 }
    );
  }
}
