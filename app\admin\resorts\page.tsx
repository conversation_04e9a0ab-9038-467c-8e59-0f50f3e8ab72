import { prisma } from "@/lib/prisma";
import Link from "next/link";

import Image from "next/image";
import { deleteResort } from "@/lib/actions";

export default async function AdminResorts() {
  const resorts = await prisma.resort.findMany();

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Resorts</h2>
        <Link
          href="/admin/resorts/create"
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          + Add Resort
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {resorts.map((resort) => (
          <div key={resort.id} className="border rounded-xl p-4">
            <Image
              src={resort.image}
              alt={resort.name}
              width={400}
              height={160}
              className="w-full h-40 object-cover rounded"
            />
            <h3 className="mt-2 text-lg font-semibold">{resort.name}</h3>
            <div className="mt-2 flex justify-between">
              <Link
                href={`/admin/resorts/${resort.id}/edit`}
                className="text-blue-500"
              >
                Edit
              </Link>
              <form action={deleteResort}>
                <input type="hidden" name="id" value={resort.id} />
                <button
                  type="submit"
                  className="text-red-500 hover:underline"
                  onClick={(e) => {
                    if (
                      !confirm("Are you sure you want to delete this resort?")
                    ) {
                      e.preventDefault();
                    }
                  }}
                >
                  Delete
                </button>
              </form>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
