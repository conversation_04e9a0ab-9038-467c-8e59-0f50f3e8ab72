import { z } from "zod";

// User role validation
export const UserRoleSchema = z.enum([
  "admin",
  "manager",
  "receptionist",
  "user",
]);

// Resort validation schemas
export const CreateResortSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  slug: z
    .string()
    .min(1, "Slug is required")
    .max(50, "Slug too long")
    .regex(
      /^[a-z0-9-]+$/,
      "Slug must contain only lowercase letters, numbers, and hyphens"
    ),
  description: z
    .string()
    .min(10, "Description must be at least 10 characters")
    .max(1000, "Description too long"),
  location: z
    .string()
    .min(1, "Location is required")
    .max(100, "Location too long"),
  image: z.string().url("Invalid image URL"),
});

export const UpdateResortSchema = CreateResortSchema.partial();

// Spa treatment validation schemas
export const CreateSpaTreatmentSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  description: z
    .string()
    .min(10, "Description must be at least 10 characters")
    .max(1000, "Description too long"),
  price: z.number().positive("Price must be positive"),
  duration: z.number().positive("Duration must be positive").optional(),
  image: z.string().url("Invalid image URL"),
});

export const UpdateSpaTreatmentSchema = CreateSpaTreatmentSchema.partial();

// Booking validation schemas
export const CreateBookingSchema = z
  .object({
    userEmail: z.string().email("Invalid email address"),
    resortId: z.string().min(1, "Resort ID required").optional(),
    spaId: z.string().min(1, "Spa ID required").optional(),
    checkIn: z.string().datetime("Invalid check-in date"),
    checkOut: z.string().datetime("Invalid check-out date").optional(),
    notes: z.string().max(500, "Notes too long").optional(),
  })
  .refine((data) => data.resortId || data.spaId, {
    message: "Either resortId or spaId must be provided",
    path: ["resortId"],
  })
  .refine(
    (data) => {
      if (data.checkOut && data.checkIn) {
        return new Date(data.checkOut) > new Date(data.checkIn);
      }
      return true;
    },
    {
      message: "Check-out date must be after check-in date",
      path: ["checkOut"],
    }
  )
  .refine(
    (data) => {
      const checkInDate = new Date(data.checkIn);
      const now = new Date();
      return checkInDate >= now;
    },
    {
      message: "Check-in date cannot be in the past",
      path: ["checkIn"],
    }
  );

export const UpdateBookingStatusSchema = z.object({
  status: z.enum(["PENDING", "CONFIRMED", "CANCELLED"]),
});

// Room validation schemas
export const CreateRoomSchema = z.object({
  resortId: z.string().min(1, "Resort ID is required"),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  type: z.string().min(1, "Type is required").max(50, "Type too long"),
  price: z.number().positive("Price must be positive"),
  image: z.string().url("Invalid image URL"),
});

export const UpdateRoomSchema = CreateRoomSchema.partial().omit({
  resortId: true,
});

// Availability check schema
export const AvailabilityCheckSchema = z.object({
  date: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  type: z.enum(["resort", "spa"]),
  id: z.string().min(1, "ID is required"),
});

// Helper function to validate request body
export function validateRequestBody<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: string } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      return { success: false, error: errorMessage };
    }
    return { success: false, error: "Invalid input data" };
  }
}

// Helper function to sanitize string inputs
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, "");
}

// Helper function to validate MongoDB ObjectId
export function isValidObjectId(id: string): boolean {
  return /^[0-9a-fA-F]{24}$/.test(id);
}
