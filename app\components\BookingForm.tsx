"use client";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

interface BookingFormProps {
  resortId?: string;
  spaId?: string;
  resortName?: string;
  spaName?: string;
}

export default function BookingForm({
  resortId,
  spaId,
  resortName,
  spaName,
}: BookingFormProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [checkIn, setCheckIn] = useState("");
  const [checkOut, setCheckOut] = useState("");
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [availability, setAvailability] = useState<{
    isAvailable: boolean;
    count: number;
    maxBookings: number;
  } | null>(null);
  const [checkingAvailability, setCheckingAvailability] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/login");
    }
  }, [status, router]);

  // Check availability when check-in date changes
  useEffect(() => {
    if (checkIn && (resortId || spaId)) {
      checkAvailability();
    }
  }, [checkIn, resortId, spaId]);

  const checkAvailability = async () => {
    if (!checkIn) return;

    setCheckingAvailability(true);
    try {
      const response = await fetch("/api/availability", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest", // CSRF protection
        },
        body: JSON.stringify({
          date: checkIn,
          type: resortId ? "resort" : "spa",
          id: resortId || spaId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAvailability(data);
      } else {
        console.error("Failed to check availability");
      }
    } catch (error) {
      console.error("Error checking availability:", error);
    } finally {
      setCheckingAvailability(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Check-in date validation
    if (!checkIn) {
      newErrors.checkIn = "Check-in date is required";
    } else {
      const checkInDate = new Date(checkIn);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (checkInDate < today) {
        newErrors.checkIn = "Check-in date cannot be in the past";
      }
    }

    // Check-out date validation (for resorts)
    if (resortId) {
      if (!checkOut) {
        newErrors.checkOut = "Check-out date is required for resort bookings";
      } else if (checkIn) {
        const checkInDate = new Date(checkIn);
        const checkOutDate = new Date(checkOut);

        if (checkOutDate <= checkInDate) {
          newErrors.checkOut = "Check-out date must be after check-in date";
        }
      }
    }

    // Notes validation
    if (notes && notes.length > 500) {
      newErrors.notes = "Notes cannot exceed 500 characters";
    }

    // Availability validation
    if (availability && !availability.isAvailable) {
      newErrors.availability = "Selected date is not available";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors before submitting");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch("/api/bookings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest", // CSRF protection
        },
        body: JSON.stringify({
          resortId,
          spaId,
          checkIn: new Date(checkIn).toISOString(),
          checkOut: checkOut ? new Date(checkOut).toISOString() : undefined,
          notes: notes.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Booking created successfully!");
        router.push("/bookings");
      } else {
        toast.error(data.error || "Failed to create booking");
      }
    } catch (error) {
      console.error("Error creating booking:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect in useEffect
  }

  const today = new Date().toISOString().split("T")[0];
  const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000)
    .toISOString()
    .split("T")[0];

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-6 bg-white p-6 rounded-lg shadow-md max-w-md mx-auto"
    >
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Book Now</h2>
        {resortName && (
          <p className="text-sm text-gray-600 mt-1">Resort: {resortName}</p>
        )}
        {spaName && (
          <p className="text-sm text-gray-600 mt-1">Treatment: {spaName}</p>
        )}
      </div>

      {/* Check-in Date */}
      <div>
        <label
          htmlFor="checkIn"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Check-in Date *
        </label>
        <input
          id="checkIn"
          type="date"
          value={checkIn}
          onChange={(e) => setCheckIn(e.target.value)}
          min={today}
          required
          className={`w-full border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            errors.checkIn ? "border-red-500" : "border-gray-300"
          }`}
        />
        {errors.checkIn && (
          <p className="text-red-500 text-xs mt-1">{errors.checkIn}</p>
        )}
      </div>

      {/* Check-out Date (for resorts only) */}
      {resortId && (
        <div>
          <label
            htmlFor="checkOut"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Check-out Date *
          </label>
          <input
            id="checkOut"
            type="date"
            value={checkOut}
            onChange={(e) => setCheckOut(e.target.value)}
            min={checkIn || tomorrow}
            required
            className={`w-full border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.checkOut ? "border-red-500" : "border-gray-300"
            }`}
          />
          {errors.checkOut && (
            <p className="text-red-500 text-xs mt-1">{errors.checkOut}</p>
          )}
        </div>
      )}

      {/* Availability Status */}
      {checkIn && (
        <div className="p-3 rounded-md bg-gray-50">
          {checkingAvailability ? (
            <div className="flex items-center text-sm text-gray-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Checking availability...
            </div>
          ) : availability ? (
            <div
              className={`text-sm ${
                availability.isAvailable ? "text-green-600" : "text-red-600"
              }`}
            >
              {availability.isAvailable ? (
                <>
                  ✓ Available ({availability.count}/{availability.maxBookings}{" "}
                  bookings)
                </>
              ) : (
                <>
                  ✗ Fully booked ({availability.count}/
                  {availability.maxBookings} bookings)
                </>
              )}
            </div>
          ) : null}
          {errors.availability && (
            <p className="text-red-500 text-xs mt-1">{errors.availability}</p>
          )}
        </div>
      )}

      {/* Notes */}
      <div>
        <label
          htmlFor="notes"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Special Notes (Optional)
        </label>
        <textarea
          id="notes"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Any special requests or notes..."
          maxLength={500}
          rows={3}
          className={`w-full border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none ${
            errors.notes ? "border-red-500" : "border-gray-300"
          }`}
        />
        <div className="flex justify-between items-center mt-1">
          {errors.notes && (
            <p className="text-red-500 text-xs">{errors.notes}</p>
          )}
          <p className="text-xs text-gray-500 ml-auto">
            {notes.length}/500 characters
          </p>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={loading || (availability && !availability.isAvailable)}
        className={`w-full py-3 px-4 rounded-md font-medium transition-colors ${
          loading || (availability && !availability.isAvailable)
            ? "bg-gray-300 text-gray-500 cursor-not-allowed"
            : "bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        }`}
      >
        {loading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Creating Booking...
          </div>
        ) : (
          "Create Booking"
        )}
      </button>

      <p className="text-xs text-gray-500 text-center">* Required fields</p>
    </form>
  );
}
