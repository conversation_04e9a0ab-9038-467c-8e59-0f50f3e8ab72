import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";
import { MAX_BOOKINGS_PER_DAY } from "@/lib/constants";
import {
  AvailabilityCheckSchema,
  validateRequestBody,
  isValidObjectId,
} from "@/lib/validation";

export async function POST(req: Request) {
  try {
    const rawData = await req.json();

    // Validate input data
    const validation = validateRequestBody(AvailabilityCheckSchema, rawData);
    if (!validation.success) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const { date, type, id } = validation.data;

    // Validate ObjectId format
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: "Invalid resource ID" },
        { status: 400 }
      );
    }

    // Verify the resource exists
    if (type === "resort") {
      const resort = await prisma.resort.findUnique({ where: { id } });
      if (!resort) {
        return NextResponse.json(
          { error: "Resort not found" },
          { status: 404 }
        );
      }
    } else if (type === "spa") {
      const spa = await prisma.spaTreatment.findUnique({ where: { id } });
      if (!spa) {
        return NextResponse.json(
          { error: "Spa treatment not found" },
          { status: 404 }
        );
      }
    }

    // Validate date is not in the past
    const checkDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (checkDate < today) {
      return NextResponse.json({
        isAvailable: false,
        count: 0,
        error: "Cannot check availability for past dates",
      });
    }

    // Check existing bookings for the date (exclude cancelled bookings)
    const bookings = await prisma.booking.findMany({
      where: {
        checkIn: {
          gte: new Date(date + "T00:00:00Z"),
          lt: new Date(date + "T23:59:59Z"),
        },
        ...(type === "resort" ? { resortId: id } : { spaId: id }),
        status: { not: "CANCELLED" }, // Don't count cancelled bookings
      },
      select: {
        id: true,
        status: true,
        checkIn: true,
      },
    });

    const isAvailable = bookings.length < MAX_BOOKINGS_PER_DAY;

    return NextResponse.json({
      isAvailable,
      count: bookings.length,
      maxBookings: MAX_BOOKINGS_PER_DAY,
      date: date,
      type: type,
      resourceId: id,
    });
  } catch (error) {
    console.error("Error checking availability:", error);
    return NextResponse.json(
      { error: "Failed to check availability" },
      { status: 500 }
    );
  }
}
