import Link from "next/link";
import { prisma } from "@/lib/prisma";
import { deleteTreatment } from "@/lib/actions";
import Image from "next/image";

export default async function AdminSpa() {
  const treatments = await prisma.spaTreatment.findMany();

  return (
    <div className="p-6">
      <div className="flex justify-between mb-6">
        <h2 className="text-2xl font-bold">Spa Treatments</h2>
        <Link
          href="/admin/spa/create"
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          + Add Treatment
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {treatments.map((t) => (
          <div key={t.id} className="border rounded-xl p-4">
            <Image src={t.image} className="w-full h-40 object-cover rounded" />
            <h3 className="mt-2 text-lg font-semibold">{t.name}</h3>
            <p className="text-sm text-gray-600">{t.price} ETB</p>
            <div className="mt-2 flex justify-between text-sm">
              <Link href={`/admin/spa/${t.id}/edit`} className="text-blue-500">
                Edit
              </Link>
              <form action={deleteTreatment}>
                <input type="hidden" name="id" value={t.id} />
                <button
                  type="submit"
                  className="text-red-500"
                  onClick={(e) => {
                    if (!confirm("Delete this treatment?")) e.preventDefault();
                  }}
                >
                  Delete
                </button>
              </form>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
