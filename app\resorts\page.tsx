import { prisma } from "@/lib/prisma";
import Image from "next/image";
import Link from "next/link";
import DynamicSpaServiceCards from "@/app/components/DynamicSpaServiceCards";
import DynamicContentSection from "@/app/components/DynamicContentSection";

export default async function ResortsListPage() {
  const resorts = await prisma.resort.findMany();

  return (
    <div className="max-w-6xl mx-auto px-4 py-10">
      {/* Resorts Section */}
      <section className="mb-16">
        <h1 className="text-3xl font-bold text-center mb-10">Our Resorts</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {resorts.map((resort) => (
            <Link
              key={resort.id}
              href={`/resorts/${resort.slug}`}
              className="block bg-white rounded-xl shadow hover:shadow-md transition"
            >
              <Image
                src={resort.image}
                alt={resort.name}
                className="w-full h-48 object-cover rounded-t-xl"
                width={300}
                height={200}
              />
              <div className="p-4 text-center font-semibold">{resort.name}</div>
            </Link>
          ))}
        </div>
      </section>

      {/* Spa Services Section */}
      <DynamicSpaServiceCards
        enableRealTimeUpdates={true}
        updateInterval={30000}
        showTitle={true}
        className="mb-16"
      />

      {/* Accommodation Section */}
      <DynamicContentSection
        title="Luxury Accommodation"
        description="Experience comfort and elegance in our carefully designed rooms and suites, each offering stunning views and premium amenities."
        apiEndpoint="/api/accommodation"
        contentType="accommodation"
        enableRealTimeUpdates={true}
        updateInterval={30000}
        maxItems={8}
        className="mb-16"
      />

      {/* Experiences Section */}
      <div id="experiences">
        <DynamicContentSection
          title="Unforgettable Experiences"
          description="Discover thrilling adventures and cultural experiences that will create lasting memories during your stay."
          apiEndpoint="/api/experience"
          contentType="experience"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={8}
          className="mb-16"
        />
      </div>

      {/* Wellness Section */}
      <div id="wellness">
        <DynamicContentSection
          title="Wellness & Fitness"
          description="Rejuvenate your body and mind with our comprehensive wellness programs, fitness classes, and therapeutic services."
          apiEndpoint="/api/wellness"
          contentType="wellness"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Dining Section */}
      <div id="dining">
        <DynamicContentSection
          title="Exceptional Dining"
          description="Savor exquisite flavors from around the world at our diverse restaurants, cafes, and bars."
          apiEndpoint="/api/dining"
          contentType="dining"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Events Section */}
      <div id="events">
        <DynamicContentSection
          title="Special Events"
          description="Host your special occasions in our elegant venues, from intimate celebrations to grand corporate events."
          apiEndpoint="/api/event"
          contentType="event"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Special Offers Section */}
      <div id="offers">
        <DynamicContentSection
          title="Special Offers & Packages"
          description="Take advantage of our exclusive deals and packages designed to make your stay even more memorable."
          apiEndpoint="/api/offer"
          contentType="offer"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={4}
          gridCols={2}
          className="mb-16"
        />
      </div>
    </div>
  );
}
