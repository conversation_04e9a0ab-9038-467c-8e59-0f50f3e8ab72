import { prisma } from "@/lib/prisma";
import Image from "next/image";
import Link from "next/link";
import DynamicSpaServiceCards from "@/app/components/DynamicSpaServiceCards";
import DynamicContentSection from "@/app/components/DynamicContentSection";
import { Suspense } from "react";

// TypeScript interface for Resort
interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
  createdAt: Date;
}

// Loading component for resort cards
function ResortCardSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow animate-pulse">
      <div className="w-full h-48 bg-gray-200 rounded-t-xl"></div>
      <div className="p-4">
        <div className="h-6 bg-gray-200 rounded mx-auto w-3/4"></div>
      </div>
    </div>
  );
}

// Resort card component
function ResortCard({ resort }: { resort: Resort }) {
  return (
    <Link
      href={`/resorts/${resort.slug}`}
      className="block bg-white rounded-xl shadow hover:shadow-md transition-shadow duration-200 group"
      aria-label={`View details for ${resort.name}`}
    >
      <div className="relative overflow-hidden rounded-t-xl">
        <Image
          src={resort.image}
          alt={resort.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-200"
          width={300}
          height={200}
          loading="lazy"
        />
        {resort.location && (
          <div className="absolute bottom-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
            {resort.location}
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="text-center font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
          {resort.name}
        </h3>
        {resort.description && (
          <p className="text-sm text-gray-600 mt-2 line-clamp-2">
            {resort.description.substring(0, 100)}...
          </p>
        )}
      </div>
    </Link>
  );
}

// Resorts grid component
async function ResortsGrid() {
  try {
    const resorts = await prisma.resort.findMany({
      orderBy: { createdAt: "desc" },
    });

    if (resorts.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🏨</div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Resorts Available
          </h3>
          <p className="text-gray-600">
            Check back soon for new resort listings.
          </p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {resorts.map((resort) => (
          <ResortCard key={resort.id} resort={resort} />
        ))}
      </div>
    );
  } catch (error) {
    console.error("Error fetching resorts:", error);
    return (
      <div className="text-center py-12">
        <div className="text-red-400 text-6xl mb-4">⚠️</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Unable to Load Resorts
        </h3>
        <p className="text-gray-600">
          Please try refreshing the page or contact support if the problem
          persists.
        </p>
      </div>
    );
  }
}

export default async function ResortsListPage() {
  return (
    <div className="max-w-6xl mx-auto px-4 py-10">
      {/* Resorts Section */}
      <section className="mb-16">
        <div className="text-center mb-10">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Our Resorts</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our collection of luxury resorts, each offering unique
            experiences and world-class amenities.
          </p>
        </div>

        <Suspense
          fallback={
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <ResortCardSkeleton key={i} />
              ))}
            </div>
          }
        >
          <ResortsGrid />
        </Suspense>
      </section>

      {/* Spa Services Section */}
      <DynamicSpaServiceCards
        enableRealTimeUpdates={true}
        updateInterval={30000}
        showTitle={true}
        className="mb-16"
      />

      {/* Accommodation Section */}
      <DynamicContentSection
        title="Luxury Accommodation"
        description="Experience comfort and elegance in our carefully designed rooms and suites, each offering stunning views and premium amenities."
        apiEndpoint="/api/accommodation"
        contentType="accommodation"
        enableRealTimeUpdates={true}
        updateInterval={30000}
        maxItems={8}
        className="mb-16"
      />

      {/* Experiences Section */}
      <div id="experiences">
        <DynamicContentSection
          title="Unforgettable Experiences"
          description="Discover thrilling adventures and cultural experiences that will create lasting memories during your stay."
          apiEndpoint="/api/experience"
          contentType="experience"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={8}
          className="mb-16"
        />
      </div>

      {/* Wellness Section */}
      <div id="wellness">
        <DynamicContentSection
          title="Wellness & Fitness"
          description="Rejuvenate your body and mind with our comprehensive wellness programs, fitness classes, and therapeutic services."
          apiEndpoint="/api/wellness"
          contentType="wellness"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Dining Section */}
      <div id="dining">
        <DynamicContentSection
          title="Exceptional Dining"
          description="Savor exquisite flavors from around the world at our diverse restaurants, cafes, and bars."
          apiEndpoint="/api/dining"
          contentType="dining"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Events Section */}
      <div id="events">
        <DynamicContentSection
          title="Special Events"
          description="Host your special occasions in our elegant venues, from intimate celebrations to grand corporate events."
          apiEndpoint="/api/event"
          contentType="event"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={6}
          gridCols={3}
          className="mb-16"
        />
      </div>

      {/* Special Offers Section */}
      <div id="offers">
        <DynamicContentSection
          title="Special Offers & Packages"
          description="Take advantage of our exclusive deals and packages designed to make your stay even more memorable."
          apiEndpoint="/api/offer"
          contentType="offer"
          enableRealTimeUpdates={true}
          updateInterval={30000}
          maxItems={4}
          gridCols={2}
          className="mb-16"
        />
      </div>
    </div>
  );
}
