import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

import { redirect } from "next/navigation";
import BookingHistory from "../components/user/BookingHistory";

export default async function BookingPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return redirect("/login");
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    include: {
      bookings: {
        include: {
          room: { include: { resort: true } },
        },
        orderBy: { checkIn: "desc" },
      },
      spaBookings: {
        include: {
          treatment: true,
        },
        orderBy: { date: "desc" },
      },
    },
  });

  return (
    <BookingHistory
      resortBookings={user?.bookings || []}
      spaBookings={user?.spaBookings || []}
    />
  );
}
