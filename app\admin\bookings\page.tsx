import { prisma } from "@/lib/prisma";

export default async function AdminBookings() {
  const bookings = await prisma.booking.findMany({
    include: { resort: true, spaTreatment: true },
    orderBy: { createdAt: "desc" },
  });

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">All Bookings</h2>
      <div className="grid gap-4">
        {bookings.map((b) => (
          <div key={b.id} className="border rounded-lg p-4">
            <p>
              <strong>User:</strong> {b.userEmail}
            </p>
            {b.resort && (
              <p>
                <strong>Resort:</strong> {b.resort.name}
              </p>
            )}
            {b.spaTreatment && (
              <p>
                <strong>Spa:</strong> {b.spaTreatment.name}
              </p>
            )}
            <p>
              <strong>Check-in:</strong> {new Date(b.checkIn).toDateString()}
            </p>
            {b.checkOut && (
              <p>
                <strong>Check-out:</strong>{" "}
                {new Date(b.checkOut).toDateString()}
              </p>
            )}
            {b.notes && (
              <p>
                <strong>Notes:</strong> {b.notes}
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
