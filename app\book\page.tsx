import { Suspense } from "react";
import { prisma } from "@/lib/prisma";
import { notFound } from "next/navigation";
import BookingForm from "@/app/components/BookingForm";
import Image from "next/image";

interface BookingPageProps {
  searchParams: {
    resortId?: string;
    roomId?: string;
    spaId?: string;
    experienceId?: string;
    wellnessId?: string;
    eventId?: string;
  };
}

export default async function BookingPage({ searchParams }: BookingPageProps) {
  const { resortId, roomId, spaId, experienceId, wellnessId, eventId } =
    searchParams;

  // Validate that at least one content ID is provided
  if (
    !resortId &&
    !roomId &&
    !spaId &&
    !experienceId &&
    !wellnessId &&
    !eventId
  ) {
    notFound();
  }

  let resort = null;
  let room = null;
  let spaTreatment = null;
  let experience = null;
  let wellnessService = null;
  let event = null;

  try {
    // Fetch resort data if resortId is provided
    if (resortId) {
      resort = await prisma.resort.findUnique({
        where: { id: resortId },
      });
      if (!resort) {
        notFound();
      }
    }

    // Fetch room data if roomId is provided
    if (roomId) {
      room = await prisma.room.findUnique({
        where: { id: roomId },
        include: { resort: true },
      });
      if (!room || !room.isActive) {
        notFound();
      }
    }

    // Fetch spa treatment data if spaId is provided
    if (spaId) {
      spaTreatment = await prisma.spaTreatment.findUnique({
        where: { id: spaId },
      });
      if (!spaTreatment || !spaTreatment.isActive) {
        notFound();
      }
    }

    // Fetch experience data if experienceId is provided
    if (experienceId) {
      experience = await prisma.experience.findUnique({
        where: { id: experienceId },
      });
      if (!experience || !experience.isActive) {
        notFound();
      }
    }

    // Fetch wellness service data if wellnessId is provided
    if (wellnessId) {
      wellnessService = await prisma.wellnessService.findUnique({
        where: { id: wellnessId },
      });
      if (!wellnessService || !wellnessService.isActive) {
        notFound();
      }
    }

    // Fetch event data if eventId is provided
    if (eventId) {
      event = await prisma.event.findUnique({
        where: { id: eventId },
      });
      if (!event || !event.isActive) {
        notFound();
      }
    }
  } catch (error) {
    console.error("Error fetching booking data:", error);
    notFound();
  }

  // Get the current booking item and type
  const currentItem =
    resort || room || spaTreatment || experience || wellnessService || event;
  const itemType = resort
    ? "resort"
    : room
    ? "accommodation"
    : spaTreatment
    ? "spa treatment"
    : experience
    ? "experience"
    : wellnessService
    ? "wellness service"
    : "event";

  return (
    <div className="max-w-4xl mx-auto px-4 py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Book {currentItem?.name}</h1>
        <p className="text-gray-600">
          Complete the form below to book your {itemType}.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Service/Resort Details */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          {resort && (
            <>
              <Image
                src={resort.image}
                alt={resort.name}
                className="w-full h-64 object-cover"
                width={400}
                height={256}
              />
              <div className="p-6">
                <h2 className="text-2xl font-semibold mb-2">{resort.name}</h2>
                <p className="text-gray-600 mb-4">{resort.description}</p>
                <div className="text-sm text-gray-500">
                  <p>
                    <strong>Location:</strong> {resort.location}
                  </p>
                </div>
              </div>
            </>
          )}

          {spaTreatment && (
            <>
              <Image
                src={spaTreatment.image}
                alt={spaTreatment.name}
                className="w-full h-64 object-cover"
                width={400}
                height={256}
              />
              <div className="p-6">
                <h2 className="text-2xl font-semibold mb-2">
                  {spaTreatment.name}
                </h2>
                <p className="text-gray-600 mb-4">{spaTreatment.description}</p>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Price:</span>
                    <span className="font-semibold text-blue-600">
                      {spaTreatment.price} ETB
                    </span>
                  </div>

                  {spaTreatment.duration && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span className="font-semibold">
                        {spaTreatment.duration} minutes
                      </span>
                    </div>
                  )}

                  {spaTreatment.category && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Category:</span>
                      <span className="font-semibold">
                        {spaTreatment.category}
                      </span>
                    </div>
                  )}
                </div>

                {spaTreatment.features && spaTreatment.features.length > 0 && (
                  <div className="mt-4">
                    <h3 className="font-semibold mb-2">Features:</h3>
                    <div className="flex flex-wrap gap-2">
                      {spaTreatment.features.map((feature, index) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Booking Form */}
        <div className="bg-white rounded-xl shadow-md p-6">
          <h3 className="text-xl font-semibold mb-6">Booking Details</h3>
          <Suspense
            fallback={
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            }
          >
            <BookingForm resortId={resortId} spaId={spaId} />
          </Suspense>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-8 bg-gray-50 rounded-xl p-6">
        <h3 className="text-lg font-semibold mb-4">Important Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">Booking Policy</h4>
            <ul className="space-y-1">
              <li>• Bookings are subject to availability</li>
              <li>• Confirmation will be sent via email</li>
              <li>• Cancellations must be made 24 hours in advance</li>
            </ul>
          </div>

          {spaTreatment && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">
                Spa Guidelines
              </h4>
              <ul className="space-y-1">
                <li>• Please arrive 15 minutes before your appointment</li>
                <li>• Bring comfortable clothing</li>
                <li>• Inform us of any allergies or medical conditions</li>
              </ul>
            </div>
          )}

          {resort && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">
                Resort Guidelines
              </h4>
              <ul className="space-y-1">
                <li>• Check-in time: 3:00 PM</li>
                <li>• Check-out time: 11:00 AM</li>
                <li>• Valid ID required at check-in</li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
