import { prisma } from "@/lib/prisma";
import Image from "next/image";

export default async function SpaPage() {
  const treatments = await prisma.spaTreatment.findMany();

  return (
    <div className="max-w-5xl mx-auto px-4 py-10">
      <h1 className="text-4xl font-bold mb-6">Boston Day Spa</h1>
      <p className="text-lg mb-6">
        Welcome to Boston Day Spa – a sanctuary of wellness. Enjoy massage,
        facials, steam, and more.
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {treatments.map((treatment) => (
          <div key={treatment.id} className="bg-white rounded-xl shadow-md p-4">
            <Image
              src={treatment.image}
              alt={treatment.name}
              className="w-full h-40 object-cover rounded-lg mb-4"
            />
            <h2 className="text-xl font-semibold">{treatment.name}</h2>
            <p className="text-sm text-gray-600 mb-2">
              {treatment.description}
            </p>
            <p className="font-bold text-blue-600">{treatment.price} ETB</p>
          </div>
        ))}
      </div>
    </div>
  );
}
