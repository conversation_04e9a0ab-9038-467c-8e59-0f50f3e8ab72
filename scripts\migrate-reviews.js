#!/usr/bin/env node

/**
 * Migration script to update the database schema with Review and Testimonial models
 * Run this script after updating the Prisma schema
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting database migration for Reviews and Testimonials...');

try {
  // Change to the project root directory
  process.chdir(path.join(__dirname, '..'));

  console.log('📋 Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  console.log('🔄 Pushing schema changes to database...');
  execSync('npx prisma db push', { stdio: 'inherit' });

  console.log('✅ Database migration completed successfully!');
  console.log('');
  console.log('📝 Next steps:');
  console.log('1. Start your development server: npm run dev');
  console.log('2. Visit /admin/reviews to manage reviews');
  console.log('3. Visit /admin/testimonials to manage testimonials');
  console.log('4. Test the review system on resort and spa pages');
  console.log('');
  console.log('🎉 Your review and testimonial system is now ready!');

} catch (error) {
  console.error('❌ Migration failed:', error.message);
  console.log('');
  console.log('🔧 Troubleshooting:');
  console.log('1. Make sure your MongoDB connection is working');
  console.log('2. Check your MONGODB_URI environment variable');
  console.log('3. Ensure you have the latest Prisma version');
  console.log('4. Try running: npx prisma db push --force-reset (WARNING: This will reset your database)');
  process.exit(1);
}
