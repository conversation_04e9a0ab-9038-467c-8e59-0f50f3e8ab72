/**
 * Comprehensive TypeScript interfaces for dashboard components
 * Ensures type safety and better development experience
 */

import { ReactNode } from "react";

// Base interfaces for common data structures
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt?: string;
}

export interface User extends BaseEntity {
  email: string;
  name?: string;
  role: "admin" | "manager" | "receptionist" | "user";
  isActive?: boolean;
}

export interface Resort extends BaseEntity {
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
  price?: number;
  amenities?: string[];
  isActive?: boolean;
}

export interface SpaTreatment extends BaseEntity {
  name: string;
  description: string;
  price: number;
  duration?: number;
  image: string;
  category?: string;
  isActive?: boolean;
}

export interface Booking extends BaseEntity {
  userEmail: string;
  userId?: string;
  resortId?: string;
  spaId?: string;
  checkIn: string;
  checkOut?: string;
  notes?: string;
  status: "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
  totalAmount?: number;
  paymentStatus?: "PENDING" | "PAID" | "REFUNDED";
  resort?: Resort;
  spaTreatment?: SpaTreatment;
  user?: User;
}

// Analytics and metrics interfaces
export interface BookingAnalytics {
  totalBookings: number;
  bookingsByStatus: Record<string, number>;
  bookingsByType: Record<string, number>;
  revenueByMonth: Array<{ month: string; revenue: number; bookings: number }>;
  revenueByQuarter: Array<{ quarter: string; revenue: number; growth: number }>;
  popularResorts: Array<{ name: string; bookings: number; revenue: number }>;
  popularSpaTreatments: Array<{
    name: string;
    bookings: number;
    revenue: number;
  }>;
  averageBookingValue: number;
  cancellationRate: number;
  occupancyRate: number;
  seasonalTrends: Array<{ period: string; bookings: number; revenue: number }>;
  customerRetentionRate: number;
  averageLeadTime: number;
}

export interface SystemMetrics {
  responseTime: number;
  errorRate: number;
  activeUsers: number;
  databaseConnections: number;
  memoryUsage: number;
  cpuUsage: number;
  uptime: number;
  requestsPerMinute: number;
  errorCount: number;
  lastUpdated: string;
}

export interface BookingPatterns {
  dayOfWeekPattern: Record<string, number>;
  hourPattern: Record<number, number>;
  monthlyPattern: Record<string, number>;
  averageLeadTime: number;
  totalBookings: number;
  peakHours: Array<{ hour: number; bookings: number }>;
  peakDays: Array<{ day: string; bookings: number }>;
}

// Dashboard component interfaces
export interface DashboardData {
  users: User[];
  bookings: Booking[];
  resorts: Resort[];
  spaTreatments: SpaTreatment[];
  bookingAnalytics: BookingAnalytics;
  systemMetrics: SystemMetrics;
  bookingPatterns: BookingPatterns;
  recentBookings: Booking[];
}

export interface StatCardChange {
  value: number;
  type: "increase" | "decrease" | "neutral";
  period: string;
  isPercentage?: boolean;
}

export interface StatCardProps {
  title: string;
  value: string | number;
  change?: StatCardChange;
  icon?: ReactNode;
  color?: "blue" | "green" | "yellow" | "red" | "purple" | "indigo" | "gray";
  loading?: boolean;
  error?: string;
  onClick?: () => void;
  className?: string;
  ariaLabel?: string;
  description?: string;
  trend?: Array<{ period: string; value: number }>;
}

// Chart data interfaces
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: Record<string, any>;
}

export interface TimeSeriesDataPoint {
  timestamp: string;
  value: number;
  label?: string;
  category?: string;
}

export interface ChartProps {
  data: ChartDataPoint[] | TimeSeriesDataPoint[];
  type: "bar" | "line" | "pie" | "area" | "donut" | "scatter";
  title?: string;
  height?: number;
  width?: number;
  className?: string;
  showValues?: boolean;
  showGrid?: boolean;
  showLegend?: boolean;
  colors?: string[];
  interactive?: boolean;
  onDataPointClick?: (dataPoint: ChartDataPoint | TimeSeriesDataPoint) => void;
  loading?: boolean;
  error?: string;
  ariaLabel?: string;
  description?: string;
}

// Data table interfaces
export interface Column<T> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: T, index: number) => ReactNode;
  width?: string;
  align?: "left" | "center" | "right";
  className?: string;
  ariaLabel?: string;
}

export interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  error?: string;
  pagination?: {
    pageSize: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
  };
  searchable?: boolean;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
  onRowClick?: (row: T, index: number) => void;
  onSelectionChange?: (selectedRows: T[]) => void;
  selectable?: boolean;
  ariaLabel?: string;
  caption?: string;
}

// Quick actions interfaces
export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: ReactNode;
  href?: string;
  onClick?: () => void;
  color?: "blue" | "green" | "yellow" | "red" | "purple" | "indigo";
  disabled?: boolean;
  loading?: boolean;
  badge?: string | number;
  ariaLabel?: string;
  requiresConfirmation?: boolean;
  confirmationMessage?: string;
}

export interface QuickActionsProps {
  actions: QuickAction[];
  title?: string;
  className?: string;
  columns?: 1 | 2 | 3 | 4 | 6;
  loading?: boolean;
  error?: string;
  ariaLabel?: string;
}

// Error handling interfaces
export interface DashboardError {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
  recoverable: boolean;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: DashboardError;
}

// Real-time update interfaces
export interface RealTimeUpdate {
  type:
    | "booking_created"
    | "booking_updated"
    | "booking_cancelled"
    | "system_metric_update";
  data: any;
  timestamp: string;
}

export interface RealTimeConfig {
  enabled: boolean;
  updateInterval: number;
  reconnectAttempts: number;
  onUpdate?: (update: RealTimeUpdate) => void;
  onError?: (error: DashboardError) => void;
}

// Filter and search interfaces
export interface FilterOption {
  key: string;
  label: string;
  value: any;
  count?: number;
}

export interface FilterConfig {
  key: string;
  label: string;
  type: "select" | "multiselect" | "date" | "daterange" | "number" | "text";
  options?: FilterOption[];
  placeholder?: string;
  defaultValue?: any;
}

export interface SearchAndFilterProps {
  filters: FilterConfig[];
  onFilterChange: (filters: Record<string, any>) => void;
  onSearchChange: (searchTerm: string) => void;
  searchPlaceholder?: string;
  className?: string;
  loading?: boolean;
}

// Accessibility interfaces
export interface AccessibilityProps {
  ariaLabel?: string;
  ariaDescribedBy?: string;
  role?: string;
  tabIndex?: number;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

// Layout interfaces
export interface DashboardLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  actions?: ReactNode;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
    current?: boolean;
  }>;
  sidebar?: ReactNode;
  className?: string;
  loading?: boolean;
  error?: DashboardError;
  onRetry?: () => void;
}

// Export utility types
export type DashboardTheme = "light" | "dark" | "auto";
export type DashboardSize = "sm" | "md" | "lg" | "xl";
export type LoadingState = "idle" | "loading" | "success" | "error";
export type SortDirection = "asc" | "desc" | null;
