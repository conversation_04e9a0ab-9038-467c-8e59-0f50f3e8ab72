"use client";

import { useEffect, useState } from "react";

// TypeScript interfaces for type safety
interface Resort {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  location: string;
}

interface SpaTreatment {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
}

interface Booking {
  id: string;
  userEmail: string;
  resortId?: string;
  spaId?: string;
  checkIn: string;
  checkOut?: string;
  createdAt: string;
  notes?: string;
  status: "pending" | "confirmed" | "cancelled";
  resort?: Resort;
  spa?: SpaTreatment;
}

export default function BookingDashboard() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        setError(null);
        const res = await fetch("/api/bookings/admin");

        if (!res.ok) {
          throw new Error(`Failed to fetch bookings: ${res.status}`);
        }

        const data = await res.json();
        setBookings(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch bookings"
        );
        console.error("Error fetching bookings:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, []);

  const handleStatusChange = async (id: string, status: string) => {
    try {
      const res = await fetch(`/api/bookings/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!res.ok) {
        throw new Error(`Failed to update booking: ${res.status}`);
      }

      // Update the local state optimistically
      setBookings((prev) =>
        prev.map((b) =>
          b.id === id ? { ...b, status: status as Booking["status"] } : b
        )
      );
    } catch (err) {
      console.error("Error updating booking status:", err);
      // You might want to show a toast notification here
      alert("Failed to update booking status. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Booking Management</h1>
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading bookings...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Booking Management</h1>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800">Error: {error}</div>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="mt-2 text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Booking Management</h1>

      {bookings.length === 0 ? (
        <div className="text-center py-8 text-gray-500">No bookings found.</div>
      ) : (
        <div className="space-y-4">
          {bookings.map((booking) => (
            <div
              key={booking.id}
              className="border p-4 rounded-lg shadow-sm space-y-1"
            >
              <p>
                <strong>User:</strong> {booking.userEmail}
              </p>
              <p>
                <strong>Type:</strong> {booking.resort ? "Resort" : "Spa"}
              </p>
              {booking.resort && (
                <p>
                  <strong>Resort:</strong> {booking.resort.name}
                </p>
              )}
              {booking.spa && (
                <p>
                  <strong>Spa Treatment:</strong> {booking.spa.name}
                </p>
              )}
              <p>
                <strong>Check-In:</strong>{" "}
                {new Date(booking.checkIn).toLocaleDateString()}
              </p>
              {booking.checkOut && (
                <p>
                  <strong>Check-Out:</strong>{" "}
                  {new Date(booking.checkOut).toLocaleDateString()}
                </p>
              )}
              {booking.notes && (
                <p>
                  <strong>Notes:</strong> {booking.notes}
                </p>
              )}
              <p>
                <strong>Status:</strong>
                <select
                  value={booking.status}
                  onChange={(e) =>
                    handleStatusChange(booking.id, e.target.value)
                  }
                  aria-label={`Change status for booking ${booking.id}`}
                  className="ml-2 border rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
