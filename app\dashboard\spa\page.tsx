"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";

export default function ResortList() {
  const [spas, setSpas] = useState([]);

  useEffect(() => {
    fetch("/api/resorts")
      .then((res) => res.json())
      .then((data) => setSpas(data));
  }, []);

  return (
    <div className="p-6">
      <div className="flex justify-between mb-4">
        <h1 className="text-2xl font-bold">Spas</h1>
        <Link href="/dashboard/resorts/new" className="btn btn-primary">
          Add Spa
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {spas.map((r: any) => (
          <div key={r.id} className="border p-4 rounded-lg shadow-sm">
            <Image
              src={r.image}
              alt={r.name}
              className="h-40 w-full object-cover mb-2 rounded"
            />
            <h2 className="text-xl font-semibold">{r.name}</h2>
            <p className="text-sm text-gray-500">{r.location}</p>
            <p className="text-sm mt-1">{r.description}</p>
            <Link
              href={`/dashboard/resorts/${r.id}/edit`}
              className="text-blue-500 mt-2 inline-block"
            >
              Edit
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}
